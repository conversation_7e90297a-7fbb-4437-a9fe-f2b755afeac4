<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreatePaymentIdentifierRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'type' => ['required', Rule::in(['phone', 'national_id', 'card', 'iban'])],
            'phone' => ['nullable', 'regex:/^09\d{9}$/', 'required_if:type,phone'],
            'national_id' => ['nullable', 'regex:/^\d{10}$/', 'required_if:type,national_id'],
            'card_number' => ['nullable', 'regex:/^\d{16}$/', 'required_if:type,card'],
            'iban' => ['nullable', 'regex:/^IR\d{24}$/', 'required_if:type,iban'],
            'amount' => ['nullable', 'numeric', 'min:1000', 'max:500000000'],
            'currency' => ['nullable', Rule::in(['IRR', 'IRT'])],
            'description' => ['nullable', 'string', 'max:500'],
            'expires_at' => ['nullable', 'date', 'after:now'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'type.required' => 'نوع شناسه پرداخت الزامی است.',
            'type.in' => 'نوع شناسه پرداخت معتبر نیست.',
            'phone.regex' => 'شماره تلفن باید با 09 شروع شده و 11 رقم باشد.',
            'phone.required_if' => 'شماره تلفن الزامی است.',
            'national_id.regex' => 'کد ملی باید 10 رقم باشد.',
            'national_id.required_if' => 'کد ملی الزامی است.',
            'card_number.regex' => 'شماره کارت باید 16 رقم باشد.',
            'card_number.required_if' => 'شماره کارت الزامی است.',
            'iban.regex' => 'شماره شبا باید با IR شروع شده و 26 کاراکتر باشد.',
            'iban.required_if' => 'شماره شبا الزامی است.',
            'amount.numeric' => 'مبلغ باید عدد باشد.',
            'amount.min' => 'حداقل مبلغ 1000 ریال است.',
            'amount.max' => 'حداکثر مبلغ 500,000,000 ریال است.',
            'currency.in' => 'واحد پول معتبر نیست.',
            'description.max' => 'توضیحات نباید بیش از 500 کاراکتر باشد.',
            'expires_at.date' => 'تاریخ انقضا معتبر نیست.',
            'expires_at.after' => 'تاریخ انقضا باید در آینده باشد.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'type' => 'نوع شناسه',
            'phone' => 'شماره تلفن',
            'national_id' => 'کد ملی',
            'card_number' => 'شماره کارت',
            'iban' => 'شماره شبا',
            'amount' => 'مبلغ',
            'currency' => 'واحد پول',
            'description' => 'توضیحات',
            'expires_at' => 'تاریخ انقضا',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Clean phone number
        if ($this->has('phone')) {
            $phone = preg_replace('/[^0-9]/', '', $this->phone);
            if (strlen($phone) === 11 && substr($phone, 0, 1) === '0') {
                $phone = substr($phone, 1);
            }
            $this->merge(['phone' => '0' . $phone]);
        }

        // Clean national ID
        if ($this->has('national_id')) {
            $nationalId = preg_replace('/[^0-9]/', '', $this->national_id);
            $this->merge(['national_id' => $nationalId]);
        }

        // Clean card number
        if ($this->has('card_number')) {
            $cardNumber = preg_replace('/[^0-9]/', '', $this->card_number);
            $this->merge(['card_number' => $cardNumber]);
        }

        // Clean IBAN
        if ($this->has('iban')) {
            $iban = strtoupper(preg_replace('/[^A-Z0-9]/', '', $this->iban));
            if (!str_starts_with($iban, 'IR')) {
                $iban = 'IR' . $iban;
            }
            $this->merge(['iban' => $iban]);
        }

        // Convert currency
        if ($this->has('currency') && $this->currency === 'T') {
            $this->merge(['currency' => 'IRT']);
        }
    }
}
