<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class VerifyJibitCallback
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Verify that the request is coming from Jibit
        if (!$this->isValidJibitCallback($request)) {
            Log::warning('Invalid Jibit callback attempt', [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 401);
        }

        return $next($request);
    }

    /**
     * Verify if the callback is from Jibit
     */
    protected function isValidJibitCallback(Request $request): bool
    {
        // Check if request has required Jibit headers or signature
        // This is a basic implementation - you should implement proper signature verification
        
        // 1. Check IP whitelist (if Jibit provides specific IPs)
        $allowedIPs = config('payment.drivers.jibit.allowed_ips', []);
        if (!empty($allowedIPs) && !in_array($request->ip(), $allowedIPs)) {
            return false;
        }

        // 2. Check User-Agent (if Jibit uses specific user agent)
        $userAgent = $request->userAgent();
        if ($userAgent && !str_contains(strtolower($userAgent), 'jibit')) {
            // This is optional - remove if Jibit doesn't use specific user agent
            // return false;
        }

        // 3. Verify signature if Jibit provides one
        $signature = $request->header('X-Jibit-Signature');
        if ($signature) {
            return $this->verifySignature($request, $signature);
        }

        // 4. Check for required fields in callback
        $requiredFields = ['id', 'status'];
        foreach ($requiredFields as $field) {
            if (!$request->has($field)) {
                Log::warning('Missing required field in Jibit callback', [
                    'missing_field' => $field,
                    'data' => $request->all()
                ]);
                return false;
            }
        }

        return true;
    }

    /**
     * Verify callback signature
     */
    protected function verifySignature(Request $request, string $signature): bool
    {
        try {
            // Get the webhook secret from config
            $secret = config('payment.drivers.jibit.webhook_secret');
            
            if (!$secret) {
                Log::warning('Jibit webhook secret not configured');
                return true; // Allow if no secret is configured
            }

            // Get the raw payload
            $payload = $request->getContent();
            
            // Calculate expected signature
            $expectedSignature = hash_hmac('sha256', $payload, $secret);
            
            // Compare signatures
            return hash_equals($expectedSignature, $signature);

        } catch (\Exception $e) {
            Log::error('Error verifying Jibit callback signature', [
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }
}
