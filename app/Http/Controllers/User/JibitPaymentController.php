<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreatePaymentIdentifierRequest;
use App\Http\Services\JibitPaymentIdentifierService;
use App\Jobs\ProcessJibitCallback;
use App\Models\PaymentIdentifier;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class JibitPaymentController extends Controller
{
    public function __construct(
        protected JibitPaymentIdentifierService $jibitService
    ) {}

    /**
     * Display a listing of user's payment identifiers.
     */
    public function index(Request $request): JsonResponse
    {
        $query = PaymentIdentifier::where('user_id', Auth::id());

        // Apply filters
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        if ($request->has('type') && $request->type) {
            $query->where('type', $request->type);
        }

        $identifiers = $query->orderBy('created_at', 'desc')->paginate(15);

        return response()->json([
            'success' => true,
            'message' => 'شناسه‌های پرداخت با موفقیت دریافت شد.',
            'data' => $identifiers
        ]);
    }

    /**
     * Store a newly created payment identifier.
     */
    public function store(CreatePaymentIdentifierRequest $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $data = $request->validated();

            // Create payment identifier
            $paymentIdentifier = $this->jibitService->createPaymentIdentifier($user, $data);

            Log::info('Payment identifier created', [
                'user_id' => $user->id,
                'identifier' => $paymentIdentifier->identifier,
                'type' => $paymentIdentifier->type
            ]);

            return response()->json([
                'success' => true,
                'message' => 'شناسه پرداخت با موفقیت ایجاد شد.',
                'data' => [
                    'identifier' => $paymentIdentifier->identifier,
                    'type' => $paymentIdentifier->type,
                    'status' => $paymentIdentifier->status,
                    'expires_at' => $paymentIdentifier->expires_at,
                    'created_at' => $paymentIdentifier->created_at,
                ]
            ], 201);

        } catch (\Exception $e) {
            Log::error('Failed to create payment identifier', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'data' => $request->validated()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'خطا در ایجاد شناسه پرداخت. لطفاً دوباره تلاش کنید.',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Display the specified payment identifier.
     */
    public function show(string $identifier): JsonResponse
    {
        $paymentIdentifier = PaymentIdentifier::where('identifier', $identifier)
            ->where('user_id', Auth::id())
            ->first();

        if (!$paymentIdentifier) {
            return response()->json([
                'success' => false,
                'message' => 'شناسه پرداخت یافت نشد.'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'اطلاعات شناسه پرداخت دریافت شد.',
            'data' => $paymentIdentifier
        ]);
    }

    /**
     * Verify a payment identifier.
     */
    public function verify(Request $request, string $identifier): JsonResponse
    {
        $request->validate([
            'transaction_id' => 'nullable|string',
            'reference_id' => 'nullable|string',
        ]);

        try {
            $paymentIdentifier = PaymentIdentifier::where('identifier', $identifier)
                ->where('user_id', Auth::id())
                ->first();

            if (!$paymentIdentifier) {
                return response()->json([
                    'success' => false,
                    'message' => 'شناسه پرداخت یافت نشد.'
                ], 404);
            }

            if ($paymentIdentifier->isExpired()) {
                return response()->json([
                    'success' => false,
                    'message' => 'شناسه پرداخت منقضی شده است.'
                ], 400);
            }

            if ($paymentIdentifier->isVerified()) {
                return response()->json([
                    'success' => true,
                    'message' => 'شناسه پرداخت قبلاً تایید شده است.',
                    'data' => $paymentIdentifier
                ]);
            }

            $verified = $this->jibitService->verifyPaymentIdentifier(
                $identifier,
                $request->only(['transaction_id', 'reference_id'])
            );

            if ($verified) {
                $paymentIdentifier->refresh();
                
                return response()->json([
                    'success' => true,
                    'message' => 'شناسه پرداخت با موفقیت تایید شد.',
                    'data' => $paymentIdentifier
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'تایید شناسه پرداخت ناموفق بود.'
            ], 400);

        } catch (\Exception $e) {
            Log::error('Failed to verify payment identifier', [
                'identifier' => $identifier,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'خطا در تایید شناسه پرداخت. لطفاً دوباره تلاش کنید.',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Get payment status from Jibit.
     */
    public function status(string $identifier): JsonResponse
    {
        try {
            $paymentIdentifier = PaymentIdentifier::where('identifier', $identifier)
                ->where('user_id', Auth::id())
                ->first();

            if (!$paymentIdentifier) {
                return response()->json([
                    'success' => false,
                    'message' => 'شناسه پرداخت یافت نشد.'
                ], 404);
            }

            // Get status from Jibit API
            $jibitStatus = $this->jibitService->getPaymentStatus($identifier);

            return response()->json([
                'success' => true,
                'message' => 'وضعیت شناسه پرداخت دریافت شد.',
                'data' => [
                    'local_status' => $paymentIdentifier->status,
                    'jibit_status' => $jibitStatus,
                    'identifier' => $paymentIdentifier->identifier,
                    'created_at' => $paymentIdentifier->created_at,
                    'verified_at' => $paymentIdentifier->verified_at,
                    'expires_at' => $paymentIdentifier->expires_at,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get payment status', [
                'identifier' => $identifier,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'خطا در دریافت وضعیت پرداخت.',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Handle Jibit callback.
     */
    public function callback(Request $request): JsonResponse
    {
        try {
            Log::info('Jibit callback received', $request->all());

            // Dispatch job to process callback asynchronously
            ProcessJibitCallback::dispatch($request->all());

            // Return immediate response to Jibit
            return response()->json([
                'success' => true,
                'message' => 'Callback received and queued for processing'
            ]);

        } catch (\Exception $e) {
            Log::error('Exception in Jibit callback', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Callback processing failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a payment identifier.
     */
    public function destroy(string $identifier): JsonResponse
    {
        try {
            $paymentIdentifier = PaymentIdentifier::where('identifier', $identifier)
                ->where('user_id', Auth::id())
                ->first();

            if (!$paymentIdentifier) {
                return response()->json([
                    'success' => false,
                    'message' => 'شناسه پرداخت یافت نشد.'
                ], 404);
            }

            if ($paymentIdentifier->isVerified()) {
                return response()->json([
                    'success' => false,
                    'message' => 'شناسه پرداخت تایید شده قابل حذف نیست.'
                ], 400);
            }

            $paymentIdentifier->delete();

            Log::info('Payment identifier deleted', [
                'identifier' => $identifier,
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'شناسه پرداخت با موفقیت حذف شد.'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to delete payment identifier', [
                'identifier' => $identifier,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'خطا در حذف شناسه پرداخت.',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }
}
