<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Services\JibitPaymentIdentifierService;
use App\Models\PaymentIdentifier;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class JibitPaymentController extends Controller
{
    public function __construct(
        protected JibitPaymentIdentifierService $jibitService
    ) {}

    /**
     * Display a listing of payment identifiers.
     */
    public function index(Request $request)
    {
        $query = PaymentIdentifier::with('user');

        // Apply filters
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        if ($request->has('type') && $request->type) {
            $query->where('type', $request->type);
        }

        if ($request->has('user_id') && $request->user_id) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('identifier', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('national_id', 'like', "%{$search}%")
                  ->orWhere('card_number', 'like', "%{$search}%")
                  ->orWhere('iban', 'like', "%{$search}%")
                  ->orWhere('transaction_id', 'like', "%{$search}%");
            });
        }

        $identifiers = $query->orderBy('created_at', 'desc')->paginate(20);

        // Get statistics
        $stats = [
            'total' => PaymentIdentifier::count(),
            'pending' => PaymentIdentifier::where('status', 'pending')->count(),
            'verified' => PaymentIdentifier::where('status', 'verified')->count(),
            'failed' => PaymentIdentifier::where('status', 'failed')->count(),
            'expired' => PaymentIdentifier::where('status', 'expired')->count(),
        ];

        return view('admin.jibit-payment.index', [
            'identifiers' => $identifiers,
            'stats' => $stats,
            'filters' => $request->only(['status', 'type', 'user_id', 'search'])
        ]);
    }

    /**
     * Display the specified payment identifier.
     */
    public function show(PaymentIdentifier $paymentIdentifier)
    {
        $paymentIdentifier->load('user');

        // Get status from Jibit API
        $jibitStatus = null;
        try {
            $jibitStatus = $this->jibitService->getPaymentStatus($paymentIdentifier->identifier);
        } catch (\Exception $e) {
            Log::warning('Failed to get Jibit status for admin view', [
                'identifier' => $paymentIdentifier->identifier,
                'error' => $e->getMessage()
            ]);
        }

        return view('admin.jibit-payment.show', [
            'paymentIdentifier' => $paymentIdentifier,
            'jibitStatus' => $jibitStatus
        ]);
    }

    /**
     * Manually verify a payment identifier.
     */
    public function verify(Request $request, PaymentIdentifier $paymentIdentifier)
    {
        try {
            if ($paymentIdentifier->isVerified()) {
                return back()->with('warning', 'این شناسه پرداخت قبلاً تایید شده است.');
            }

            if ($paymentIdentifier->isExpired()) {
                return back()->with('error', 'این شناسه پرداخت منقضی شده است.');
            }

            $verified = $this->jibitService->verifyPaymentIdentifier(
                $paymentIdentifier->identifier,
                $request->only(['transaction_id', 'reference_id'])
            );

            if ($verified) {
                Log::info('Payment identifier manually verified by admin', [
                    'identifier' => $paymentIdentifier->identifier,
                    'admin_id' => auth()->id(),
                    'user_id' => $paymentIdentifier->user_id
                ]);

                return back()->with('success', 'شناسه پرداخت با موفقیت تایید شد.');
            }

            return back()->with('error', 'تایید شناسه پرداخت ناموفق بود.');

        } catch (\Exception $e) {
            Log::error('Failed to manually verify payment identifier', [
                'identifier' => $paymentIdentifier->identifier,
                'admin_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return back()->with('error', 'خطا در تایید شناسه پرداخت: ' . $e->getMessage());
        }
    }

    /**
     * Manually mark a payment identifier as failed.
     */
    public function markAsFailed(PaymentIdentifier $paymentIdentifier)
    {
        try {
            if ($paymentIdentifier->isVerified()) {
                return back()->with('warning', 'شناسه پرداخت تایید شده قابل تغییر وضعیت نیست.');
            }

            $paymentIdentifier->markAsFailed();

            Log::info('Payment identifier manually marked as failed by admin', [
                'identifier' => $paymentIdentifier->identifier,
                'admin_id' => auth()->id(),
                'user_id' => $paymentIdentifier->user_id
            ]);

            return back()->with('success', 'شناسه پرداخت به عنوان ناموفق علامت‌گذاری شد.');

        } catch (\Exception $e) {
            Log::error('Failed to mark payment identifier as failed', [
                'identifier' => $paymentIdentifier->identifier,
                'admin_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return back()->with('error', 'خطا در تغییر وضعیت شناسه پرداخت.');
        }
    }

    /**
     * Get payment status from Jibit API.
     */
    public function getStatus(PaymentIdentifier $paymentIdentifier)
    {
        try {
            $jibitStatus = $this->jibitService->getPaymentStatus($paymentIdentifier->identifier);

            return response()->json([
                'success' => true,
                'data' => [
                    'local_status' => $paymentIdentifier->status,
                    'jibit_status' => $jibitStatus,
                    'identifier' => $paymentIdentifier->identifier,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطا در دریافت وضعیت از جیبیت: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export payment identifiers to Excel.
     */
    public function export(Request $request)
    {
        // This would require creating an export class
        // For now, return a simple CSV response
        
        $query = PaymentIdentifier::with('user');

        // Apply same filters as index
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        if ($request->has('type') && $request->type) {
            $query->where('type', $request->type);
        }

        $identifiers = $query->orderBy('created_at', 'desc')->get();

        $filename = 'jibit_payment_identifiers_' . now()->format('Y_m_d_H_i_s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function () use ($identifiers) {
            $file = fopen('php://output', 'w');
            
            // Add BOM for UTF-8
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));
            
            // Headers
            fputcsv($file, [
                'شناسه',
                'کاربر',
                'نوع',
                'وضعیت',
                'مبلغ',
                'تاریخ ایجاد',
                'تاریخ تایید',
                'تاریخ انقضا'
            ]);

            foreach ($identifiers as $identifier) {
                fputcsv($file, [
                    $identifier->identifier,
                    $identifier->user->name ?? $identifier->user->phone,
                    $identifier->type_label,
                    $identifier->status_label,
                    $identifier->formatted_amount,
                    $identifier->created_at->format('Y-m-d H:i:s'),
                    $identifier->verified_at?->format('Y-m-d H:i:s') ?? '-',
                    $identifier->expires_at?->format('Y-m-d H:i:s') ?? '-',
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Delete a payment identifier.
     */
    public function destroy(PaymentIdentifier $paymentIdentifier)
    {
        try {
            if ($paymentIdentifier->isVerified()) {
                return back()->with('warning', 'شناسه پرداخت تایید شده قابل حذف نیست.');
            }

            $identifier = $paymentIdentifier->identifier;
            $userId = $paymentIdentifier->user_id;
            
            $paymentIdentifier->delete();

            Log::info('Payment identifier deleted by admin', [
                'identifier' => $identifier,
                'admin_id' => auth()->id(),
                'user_id' => $userId
            ]);

            return back()->with('success', 'شناسه پرداخت با موفقیت حذف شد.');

        } catch (\Exception $e) {
            Log::error('Failed to delete payment identifier', [
                'identifier' => $paymentIdentifier->identifier,
                'admin_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return back()->with('error', 'خطا در حذف شناسه پرداخت.');
        }
    }
}
