<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class PaymentIdentifier extends Model
{
    use HasFactory;

    protected $fillable = [
        'identifier',
        'user_id',
        'phone',
        'national_id',
        'card_number',
        'iban',
        'status',
        'type',
        'amount',
        'currency',
        'transaction_id',
        'reference_id',
        'description',
        'metadata',
        'verified_at',
        'expires_at',
    ];

    protected $casts = [
        'metadata' => 'array',
        'amount' => 'decimal:2',
        'verified_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    /**
     * Get the user that owns the payment identifier.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if the payment identifier is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Check if the payment identifier is verified.
     */
    public function isVerified(): bool
    {
        return $this->status === 'verified';
    }

    /**
     * Check if the payment identifier is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Mark the payment identifier as verified.
     */
    public function markAsVerified(): void
    {
        $this->update([
            'status' => 'verified',
            'verified_at' => now(),
        ]);
    }

    /**
     * Mark the payment identifier as failed.
     */
    public function markAsFailed(): void
    {
        $this->update(['status' => 'failed']);
    }

    /**
     * Mark the payment identifier as expired.
     */
    public function markAsExpired(): void
    {
        $this->update(['status' => 'expired']);
    }

    /**
     * Generate a unique payment identifier.
     */
    public static function generateIdentifier(): string
    {
        do {
            $identifier = 'JPI_' . strtoupper(uniqid());
        } while (self::where('identifier', $identifier)->exists());

        return $identifier;
    }

    /**
     * Scope for active (non-expired, non-failed) identifiers.
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['pending', 'verified'])
                    ->where(function ($q) {
                        $q->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                    });
    }

    /**
     * Scope for verified identifiers.
     */
    public function scopeVerified($query)
    {
        return $query->where('status', 'verified');
    }

    /**
     * Scope for pending identifiers.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Get formatted amount with currency.
     */
    public function getFormattedAmountAttribute(): string
    {
        if (!$this->amount) {
            return 'نامشخص';
        }

        $currency = $this->currency === 'IRR' ? 'ریال' : $this->currency;
        return number_format($this->amount) . ' ' . $currency;
    }

    /**
     * Get type label in Persian.
     */
    public function getTypeLabelAttribute(): string
    {
        return match($this->type) {
            'phone' => 'شماره تلفن',
            'national_id' => 'کد ملی',
            'card' => 'شماره کارت',
            'iban' => 'شماره شبا',
            default => 'نامشخص'
        };
    }

    /**
     * Get status label in Persian.
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'pending' => 'در انتظار تایید',
            'verified' => 'تایید شده',
            'failed' => 'ناموفق',
            'expired' => 'منقضی شده',
            default => 'نامشخص'
        };
    }
}
