<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_identifiers', function (Blueprint $table) {
            $table->id();
            $table->string('identifier')->unique(); // شناسه پرداخت جیبیت
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('phone')->nullable(); // شماره تلفن مرتبط
            $table->string('national_id')->nullable(); // کد ملی
            $table->string('card_number')->nullable(); // شماره کارت
            $table->string('iban')->nullable(); // شماره شبا
            $table->enum('status', ['pending', 'verified', 'failed', 'expired'])->default('pending');
            $table->enum('type', ['phone', 'national_id', 'card', 'iban']); // نوع شناسه
            $table->decimal('amount', 15, 2)->nullable(); // مبلغ تراکنش
            $table->string('currency', 3)->default('IRR'); // واحد پول
            $table->string('transaction_id')->nullable(); // شناسه تراکنش جیبیت
            $table->string('reference_id')->nullable(); // شناسه مرجع
            $table->text('description')->nullable(); // توضیحات
            $table->json('metadata')->nullable(); // اطلاعات اضافی
            $table->timestamp('verified_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index(['identifier', 'status']);
            $table->index('transaction_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_identifiers');
    }
};
