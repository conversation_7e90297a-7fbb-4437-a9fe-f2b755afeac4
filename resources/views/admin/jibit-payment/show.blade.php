@extends('admin.master')

@section('title')
    جزئیات شناسه پرداخت جیبیت
@endsection

@section('content')
    <div class="custom-breadcrumb">
        <div class="row">
            <div class="col-9">
                <ul>
                    <li><a href="{{ route('admin.dashboard') }}">{{ __('داشبورد') }}</a></li>
                    <li><a href="{{ route('admin.jibit-payment.index') }}">{{ __('شناسه‌های پرداخت جیبیت') }}</a></li>
                    <li class="active-breadcrumb">{{ __('جزئیات شناسه پرداخت') }}</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="user-management">
        <div class="row">
            <div class="col-12">
                <div class="header-bar">
                    <div class="table-title">
                        <h3>{{ __('جزئیات شناسه پرداخت جیبیت') }}</h3>
                    </div>
                    <div class="btn-area">
                        <a href="{{ route('admin.jibit-payment.index') }}" class="btn theme-btn">
                            <i class="fa fa-arrow-left"></i> بازگشت
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Payment Identifier Details -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5>اطلاعات شناسه پرداخت</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>شناسه:</strong></label>
                                    <p><code>{{ $paymentIdentifier->identifier }}</code></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>وضعیت:</strong></label>
                                    <p>
                                        @switch($paymentIdentifier->status)
                                            @case('pending')
                                                <span class="badge badge-warning">{{ $paymentIdentifier->status_label }}</span>
                                                @break
                                            @case('verified')
                                                <span class="badge badge-success">{{ $paymentIdentifier->status_label }}</span>
                                                @break
                                            @case('failed')
                                                <span class="badge badge-danger">{{ $paymentIdentifier->status_label }}</span>
                                                @break
                                            @case('expired')
                                                <span class="badge badge-secondary">{{ $paymentIdentifier->status_label }}</span>
                                                @break
                                        @endswitch
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>نوع شناسه:</strong></label>
                                    <p><span class="badge badge-info">{{ $paymentIdentifier->type_label }}</span></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>مقدار شناسه:</strong></label>
                                    <p>
                                        @switch($paymentIdentifier->type)
                                            @case('phone')
                                                {{ $paymentIdentifier->phone }}
                                                @break
                                            @case('national_id')
                                                {{ $paymentIdentifier->national_id }}
                                                @break
                                            @case('card')
                                                {{ $paymentIdentifier->card_number }}
                                                @break
                                            @case('iban')
                                                {{ $paymentIdentifier->iban }}
                                                @break
                                        @endswitch
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>مبلغ:</strong></label>
                                    <p>{{ $paymentIdentifier->formatted_amount }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>واحد پول:</strong></label>
                                    <p>{{ $paymentIdentifier->currency }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>شناسه تراکنش:</strong></label>
                                    <p>{{ $paymentIdentifier->transaction_id ?? 'ندارد' }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>شناسه مرجع:</strong></label>
                                    <p>{{ $paymentIdentifier->reference_id ?? 'ندارد' }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label><strong>توضیحات:</strong></label>
                            <p>{{ $paymentIdentifier->description ?? 'ندارد' }}</p>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label><strong>تاریخ ایجاد:</strong></label>
                                    <p>{{ $paymentIdentifier->created_at->format('Y/m/d H:i:s') }}</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label><strong>تاریخ تایید:</strong></label>
                                    <p>{{ $paymentIdentifier->verified_at?->format('Y/m/d H:i:s') ?? 'تایید نشده' }}</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label><strong>تاریخ انقضا:</strong></label>
                                    <p>{{ $paymentIdentifier->expires_at?->format('Y/m/d H:i:s') ?? 'ندارد' }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Jibit Status -->
                @if($jibitStatus)
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>وضعیت در جیبیت</h5>
                    </div>
                    <div class="card-body">
                        <pre>{{ json_encode($jibitStatus, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</pre>
                    </div>
                </div>
                @endif

                <!-- Metadata -->
                @if($paymentIdentifier->metadata)
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>اطلاعات اضافی</h5>
                    </div>
                    <div class="card-body">
                        <pre>{{ json_encode($paymentIdentifier->metadata, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</pre>
                    </div>
                </div>
                @endif
            </div>

            <!-- User Information & Actions -->
            <div class="col-lg-4">
                <!-- User Info -->
                <div class="card">
                    <div class="card-header">
                        <h5>اطلاعات کاربر</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label><strong>نام:</strong></label>
                            <p>{{ $paymentIdentifier->user->name ?? 'ندارد' }}</p>
                        </div>
                        <div class="form-group">
                            <label><strong>شماره تلفن:</strong></label>
                            <p>{{ $paymentIdentifier->user->phone }}</p>
                        </div>
                        <div class="form-group">
                            <label><strong>ایمیل:</strong></label>
                            <p>{{ $paymentIdentifier->user->email ?? 'ندارد' }}</p>
                        </div>
                        <div class="form-group">
                            <label><strong>کد ملی:</strong></label>
                            <p>{{ $paymentIdentifier->user->national_id ?? 'ندارد' }}</p>
                        </div>
                        <a href="{{ route('admin.user.show', $paymentIdentifier->user_id) }}" class="btn btn-info btn-sm">
                            <i class="fa fa-user"></i> مشاهده پروفایل کاربر
                        </a>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>عملیات</h5>
                    </div>
                    <div class="card-body">
                        @if($paymentIdentifier->status === 'pending')
                            <button type="button" class="btn btn-success btn-block mb-2" onclick="verifyPayment()">
                                <i class="fa fa-check"></i> تایید شناسه پرداخت
                            </button>
                            <button type="button" class="btn btn-danger btn-block mb-2" onclick="markAsFailed()">
                                <i class="fa fa-times"></i> علامت‌گذاری به عنوان ناموفق
                            </button>
                        @endif

                        <button type="button" class="btn btn-info btn-block mb-2" onclick="refreshStatus()">
                            <i class="fa fa-refresh"></i> بروزرسانی وضعیت از جیبیت
                        </button>

                        @if(!$paymentIdentifier->isVerified())
                            <button type="button" class="btn btn-danger btn-block" onclick="deletePayment()">
                                <i class="fa fa-trash"></i> حذف شناسه پرداخت
                            </button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Verify Modal -->
    <div class="modal fade" id="verifyModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تایید شناسه پرداخت</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form action="{{ route('admin.jibit-payment.verify', $paymentIdentifier) }}" method="POST">
                    @csrf
                    <div class="modal-body">
                        <p>آیا از تایید این شناسه پرداخت اطمینان دارید؟</p>
                        <div class="form-group">
                            <label>شناسه تراکنش (اختیاری)</label>
                            <input type="text" name="transaction_id" class="form-control" value="{{ $paymentIdentifier->transaction_id }}">
                        </div>
                        <div class="form-group">
                            <label>شناسه مرجع (اختیاری)</label>
                            <input type="text" name="reference_id" class="form-control" value="{{ $paymentIdentifier->reference_id }}">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">انصراف</button>
                        <button type="submit" class="btn btn-success">تایید</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@section('script')
<script>
    function verifyPayment() {
        $('#verifyModal').modal('show');
    }

    function markAsFailed() {
        if (confirm('آیا از علامت‌گذاری این شناسه به عنوان ناموفق اطمینان دارید؟')) {
            $.ajax({
                url: '{{ route("admin.jibit-payment.mark-failed", $paymentIdentifier) }}',
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    location.reload();
                },
                error: function() {
                    alert('خطا در انجام عملیات');
                }
            });
        }
    }

    function deletePayment() {
        if (confirm('آیا از حذف این شناسه پرداخت اطمینان دارید؟')) {
            $.ajax({
                url: '{{ route("admin.jibit-payment.destroy", $paymentIdentifier) }}',
                method: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    window.location.href = '{{ route("admin.jibit-payment.index") }}';
                },
                error: function() {
                    alert('خطا در حذف شناسه پرداخت');
                }
            });
        }
    }

    function refreshStatus() {
        $.ajax({
            url: '{{ route("admin.jibit-payment.status", $paymentIdentifier) }}',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    alert('وضعیت بروزرسانی شد');
                    location.reload();
                } else {
                    alert('خطا در دریافت وضعیت: ' + response.message);
                }
            },
            error: function() {
                alert('خطا در دریافت وضعیت از جیبیت');
            }
        });
    }
</script>
@endsection
