@extends('admin.master')

@section('title')
    مدیریت شناسه‌های پرداخت جیبیت
@endsection

@section('content')
    <div class="custom-breadcrumb">
        <div class="row">
            <div class="col-9">
                <ul>
                    <li>{{ __('داشبورد') }}</li>
                    <li class="active-breadcrumb">{{ __('شناسه‌های پرداخت جیبیت') }}</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="user-management">
        <div class="row">
            <div class="col-12">
                <div class="header-bar">
                    <div class="table-title">
                        <h3>{{ __('مدیریت شناسه‌های پرداخت جیبیت') }}</h3>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    کل شناسه‌ها
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['total']) }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-id-card fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    در انتظار تایید
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['pending']) }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-clock fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    تایید شده
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['verified']) }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-danger shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                    ناموفق
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['failed']) }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="{{ route('admin.jibit-payment.index') }}">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>وضعیت</label>
                                <select name="status" class="form-control">
                                    <option value="">همه</option>
                                    <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>در انتظار تایید</option>
                                    <option value="verified" {{ request('status') == 'verified' ? 'selected' : '' }}>تایید شده</option>
                                    <option value="failed" {{ request('status') == 'failed' ? 'selected' : '' }}>ناموفق</option>
                                    <option value="expired" {{ request('status') == 'expired' ? 'selected' : '' }}>منقضی شده</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>نوع شناسه</label>
                                <select name="type" class="form-control">
                                    <option value="">همه</option>
                                    <option value="phone" {{ request('type') == 'phone' ? 'selected' : '' }}>شماره تلفن</option>
                                    <option value="national_id" {{ request('type') == 'national_id' ? 'selected' : '' }}>کد ملی</option>
                                    <option value="card" {{ request('type') == 'card' ? 'selected' : '' }}>شماره کارت</option>
                                    <option value="iban" {{ request('type') == 'iban' ? 'selected' : '' }}>شماره شبا</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>جستجو</label>
                                <input type="text" name="search" class="form-control" placeholder="شناسه، تلفن، کد ملی، کارت یا شبا" value="{{ request('search') }}">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn theme-btn">فیلتر</button>
                                    <a href="{{ route('admin.jibit-payment.index') }}" class="btn btn-secondary">پاک کردن</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Export Button -->
        <div class="row mb-3">
            <div class="col-12">
                <a href="{{ route('admin.jibit-payment.export', request()->query()) }}" class="btn btn-success">
                    <i class="fa fa-download"></i> خروجی Excel
                </a>
            </div>
        </div>

        <!-- Payment Identifiers Table -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>شناسه</th>
                                <th>کاربر</th>
                                <th>نوع</th>
                                <th>مقدار</th>
                                <th>مبلغ</th>
                                <th>وضعیت</th>
                                <th>تاریخ ایجاد</th>
                                <th>عملیات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($identifiers as $identifier)
                                <tr>
                                    <td>
                                        <code>{{ $identifier->identifier }}</code>
                                    </td>
                                    <td>
                                        <a href="{{ route('admin.user.show', $identifier->user_id) }}">
                                            {{ $identifier->user->name ?? $identifier->user->phone }}
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge badge-info">{{ $identifier->type_label }}</span>
                                    </td>
                                    <td>
                                        @switch($identifier->type)
                                            @case('phone')
                                                {{ $identifier->phone }}
                                                @break
                                            @case('national_id')
                                                {{ $identifier->national_id }}
                                                @break
                                            @case('card')
                                                {{ $identifier->card_number }}
                                                @break
                                            @case('iban')
                                                {{ $identifier->iban }}
                                                @break
                                        @endswitch
                                    </td>
                                    <td>{{ $identifier->formatted_amount }}</td>
                                    <td>
                                        @switch($identifier->status)
                                            @case('pending')
                                                <span class="badge badge-warning">{{ $identifier->status_label }}</span>
                                                @break
                                            @case('verified')
                                                <span class="badge badge-success">{{ $identifier->status_label }}</span>
                                                @break
                                            @case('failed')
                                                <span class="badge badge-danger">{{ $identifier->status_label }}</span>
                                                @break
                                            @case('expired')
                                                <span class="badge badge-secondary">{{ $identifier->status_label }}</span>
                                                @break
                                        @endswitch
                                    </td>
                                    <td>{{ $identifier->created_at->format('Y/m/d H:i') }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.jibit-payment.show', $identifier) }}" class="btn btn-sm btn-info">
                                                <i class="fa fa-eye"></i>
                                            </a>
                                            @if($identifier->status === 'pending')
                                                <button type="button" class="btn btn-sm btn-success" onclick="verifyPayment('{{ $identifier->id }}')">
                                                    <i class="fa fa-check"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" onclick="markAsFailed('{{ $identifier->id }}')">
                                                    <i class="fa fa-times"></i>
                                                </button>
                                            @endif
                                            @if(!$identifier->isVerified())
                                                <button type="button" class="btn btn-sm btn-danger" onclick="deletePayment('{{ $identifier->id }}')">
                                                    <i class="fa fa-trash"></i>
                                                </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="8" class="text-center">هیچ شناسه پرداختی یافت نشد.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    {{ $identifiers->appends(request()->query())->links() }}
                </div>
            </div>
        </div>
    </div>

    <!-- Verify Modal -->
    <div class="modal fade" id="verifyModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تایید شناسه پرداخت</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="verifyForm" method="POST">
                    @csrf
                    <div class="modal-body">
                        <p>آیا از تایید این شناسه پرداخت اطمینان دارید؟</p>
                        <div class="form-group">
                            <label>شناسه تراکنش (اختیاری)</label>
                            <input type="text" name="transaction_id" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>شناسه مرجع (اختیاری)</label>
                            <input type="text" name="reference_id" class="form-control">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">انصراف</button>
                        <button type="submit" class="btn btn-success">تایید</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@section('script')
<script>
    function verifyPayment(id) {
        $('#verifyForm').attr('action', `/admin/jibit-payment/${id}/verify`);
        $('#verifyModal').modal('show');
    }

    function markAsFailed(id) {
        if (confirm('آیا از علامت‌گذاری این شناسه به عنوان ناموفق اطمینان دارید؟')) {
            $.ajax({
                url: `/admin/jibit-payment/${id}/mark-failed`,
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    location.reload();
                },
                error: function() {
                    alert('خطا در انجام عملیات');
                }
            });
        }
    }

    function deletePayment(id) {
        if (confirm('آیا از حذف این شناسه پرداخت اطمینان دارید؟')) {
            $.ajax({
                url: `/admin/jibit-payment/${id}`,
                method: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    location.reload();
                },
                error: function() {
                    alert('خطا در حذف شناسه پرداخت');
                }
            });
        }
    }
</script>
@endsection
